'use strict';
Object.defineProperty(exports, '__esModule', { value: true });
var prefix = 'fas';
var iconName = 'manat-sign';
var width = 384;
var height = 512;
var aliases = [];
var unicode = 'e1d5';
var svgPathData = 'M192 32c-17.7 0-32 14.3-32 32l0 34.7C69.2 113.9 0 192.9 0 288L0 448c0 17.7 14.3 32 32 32s32-14.3 32-32l0-160c0-59.6 40.8-109.8 96-124l0 284c0 17.7 14.3 32 32 32s32-14.3 32-32l0-284c55.2 14.2 96 64.3 96 124l0 160c0 17.7 14.3 32 32 32s32-14.3 32-32l0-160c0-95.1-69.2-174.1-160-189.3L224 64c0-17.7-14.3-32-32-32z';

exports.definition = {
  prefix: prefix,
  iconName: iconName,
  icon: [
    width,
    height,
    aliases,
    unicode,
    svgPathData
  ]};

exports.faManatSign = exports.definition;
exports.prefix = prefix;
exports.iconName = iconName;
exports.width = width;
exports.height = height;
exports.ligatures = aliases;
exports.unicode = unicode;
exports.svgPathData = svgPathData;
exports.aliases = aliases;