.cab-form-admin {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  overflow: hidden;
  margin-bottom: 2rem;
}

.form-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  text-align: center;
}

.form-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
}

.admin-form {
  padding: 2rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.95rem;
}

.form-control {
  width: 100%;
  padding: 0.875rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: #fafbfc;
}

.form-control:focus {
  outline: none;
  border-color: #667eea;
  background-color: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control.error {
  border-color: #e74c3c;
  background-color: #fdf2f2;
}

.form-control.error:focus {
  border-color: #e74c3c;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.error-message {
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  font-weight: 500;
}

/* File Input Styling */
input[type="file"].form-control {
  padding: 0.5rem;
  cursor: pointer;
}

.image-preview {
  margin-top: 1rem;
  text-align: center;
}

.image-preview img {
  max-width: 200px;
  max-height: 150px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  object-fit: cover;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #eee;
}

.form-actions .btn {
  min-width: 120px;
  padding: 0.875rem 1.5rem;
  font-weight: 600;
}

/* Number Input Styling */
input[type="number"].form-control {
  -moz-appearance: textfield;
}

input[type="number"].form-control::-webkit-outer-spin-button,
input[type="number"].form-control::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Loading State */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .cab-form-admin {
    margin: 0;
    border-radius: 8px;
  }
  
  .form-header {
    padding: 1rem;
  }
  
  .form-header h3 {
    font-size: 1.25rem;
  }
  
  .admin-form {
    padding: 1.5rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }
  
  .form-actions {
    flex-direction: column-reverse;
  }
  
  .form-actions .btn {
    width: 100%;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .form-header {
    padding: 0.75rem;
  }
  
  .form-header h3 {
    font-size: 1.1rem;
  }
  
  .admin-form {
    padding: 1rem;
  }
  
  .form-control {
    padding: 0.75rem;
    font-size: 0.95rem;
  }
  
  .image-preview img {
    max-width: 150px;
    max-height: 100px;
  }
}
