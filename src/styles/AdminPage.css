.admin-page {
  min-height: calc(100vh - 200px);
  padding: 2rem 0;
  background-color: #f8f9fa;
}

/* Admin Header */
.admin-header {
  text-align: center;
  margin-bottom: 3rem;
}

.admin-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.admin-header p {
  font-size: 1.1rem;
  color: #666;
}

/* Tabs */
.admin-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 3rem;
  background: white;
  border-radius: 12px;
  padding: 0.5rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 3rem;
}

.tab-button {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  background: transparent;
  color: #666;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.tab-icon {
  font-size: 1.1rem;
}

.tab-button:hover {
  background-color: #f8f9fa;
  color: #2c3e50;
}

.tab-button.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* Section Header */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.section-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.75rem;
}

.count-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

/* Loading and Empty States */
.loading {
  text-align: center;
  padding: 4rem 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.loading p {
  margin-top: 1rem;
  color: #666;
  font-size: 1.1rem;
}

.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.empty-state h3 {
  color: #666;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.empty-state p {
  color: #888;
  font-size: 1rem;
}

/* Bookings Table */
.bookings-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow: hidden;
}

.bookings-table {
  width: 100%;
  border-collapse: collapse;
}

.bookings-table th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.95rem;
}

.bookings-table td {
  padding: 1rem;
  border-bottom: 1px solid #eee;
  vertical-align: top;
}

.bookings-table tr:hover {
  background-color: #f8f9fa;
}

.customer-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.customer-info strong {
  color: #2c3e50;
  font-weight: 600;
}

.customer-info span {
  color: #666;
  font-size: 0.9rem;
}

.journey-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.journey-info div {
  color: #555;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.journey-icon {
  color: #3498db;
  font-size: 0.8rem;
  width: 12px;
}

.datetime-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.9rem;
}

.datetime-info div:first-child {
  font-weight: 600;
  color: #2c3e50;
}

.datetime-info div:last-child {
  color: #666;
}

.vehicle-type {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

.status-select {
  padding: 0.5rem;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  font-size: 0.9rem;
}

.status-select.status-pending {
  background-color: #fff3cd;
  color: #856404;
  border-color: #ffeaa7;
}

.status-select.status-confirmed {
  background-color: #d4edda;
  color: #155724;
  border-color: #c3e6cb;
}

.status-select.status-completed {
  background-color: #cce5ff;
  color: #004085;
  border-color: #b3d7ff;
}

.status-select.status-cancelled {
  background-color: #f8d7da;
  color: #721c24;
  border-color: #f5c6cb;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  min-width: auto;
}

/* Vehicles Grid */
.vehicles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.vehicle-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: transform 0.3s ease;
}

.vehicle-card:hover {
  transform: translateY(-5px);
}

.vehicle-image {
  height: 150px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.vehicle-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.placeholder-icon {
  font-size: 3rem;
  color: white;
  opacity: 0.9;
}

.vehicle-details {
  padding: 1.5rem;
}

.vehicle-details h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.25rem;
}

.vehicle-specs {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.vehicle-specs span {
  background: #f8f9fa;
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
  font-size: 0.9rem;
  color: #555;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.spec-icon {
  color: #3498db;
  font-size: 0.8rem;
}

.vehicle-features {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.vehicle-actions {
  display: flex;
  gap: 0.75rem;
}

.vehicle-actions .btn {
  flex: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-page {
    padding: 1rem 0;
  }
  
  .admin-header h1 {
    font-size: 2rem;
  }
  
  .admin-tabs {
    margin: 0 1rem 2rem 1rem;
    max-width: none;
  }
  
  .section-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
    margin: 0 1rem 2rem 1rem;
  }
  
  .bookings-table-container {
    margin: 0 1rem;
    overflow-x: auto;
  }
  
  .bookings-table {
    min-width: 800px;
  }
  
  .vehicles-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin: 0 1rem;
  }
}

@media (max-width: 480px) {
  .admin-header h1 {
    font-size: 1.75rem;
  }
  
  .tab-button {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
  
  .section-header h2 {
    font-size: 1.5rem;
  }
  
  .bookings-table th,
  .bookings-table td {
    padding: 0.75rem 0.5rem;
  }
  
  .vehicle-details {
    padding: 1rem;
  }
  
  .vehicle-actions {
    flex-direction: column;
  }
}
