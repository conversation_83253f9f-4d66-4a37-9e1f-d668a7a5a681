.cab-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0,0,0,0.12);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0,0,0,0.05);
}

.cab-card:hover {
  transform: translateY(-12px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.cab-image {
  height: 220px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.cab-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
  display: block;
}

.cab-card:hover .cab-image img {
  transform: scale(1.08);
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

.placeholder-icon {
  font-size: 4rem;
  color: white;
  opacity: 0.9;
}

.placeholder-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.cab-details {
  padding: 2rem 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  background: linear-gradient(to bottom, #ffffff 0%, #fafbfc 100%);
}

.cab-type {
  font-size: 1.6rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
  letter-spacing: -0.5px;
}

.cab-info {
  flex: 1;
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f0f1f3;
  transition: all 0.2s ease;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item:hover {
  background-color: rgba(52, 152, 219, 0.05);
  margin: 0 -0.5rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  border-radius: 6px;
}

.info-label {
  font-weight: 600;
  color: #555;
  font-size: 0.95rem;
}

.info-value {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.book-btn {
  width: 100%;
  padding: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 12px;
  margin-top: auto;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  border: none;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.book-btn:hover {
  background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(52, 152, 219, 0.4);
}

.book-btn:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .cab-image {
    height: 200px;
  }

  .placeholder-icon {
    font-size: 3.5rem;
  }
}

@media (max-width: 768px) {
  .cab-image {
    height: 180px;
  }

  .cab-details {
    padding: 1.5rem 1rem;
  }

  .cab-type {
    font-size: 1.4rem;
    margin-bottom: 1rem;
  }

  .placeholder-icon {
    font-size: 3rem;
  }

  .book-btn {
    padding: 0.875rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .cab-image {
    height: 160px;
  }

  .cab-details {
    padding: 1.25rem 0.875rem;
  }

  .cab-type {
    font-size: 1.25rem;
  }

  .placeholder-icon {
    font-size: 2.5rem;
  }

  .info-item {
    padding: 0.5rem 0;
  }

  .info-label,
  .info-value {
    font-size: 0.9rem;
  }
}
