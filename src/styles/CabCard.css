.cab-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.cab-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.cab-image {
  height: 200px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.cab-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.cab-card:hover .cab-image img {
  transform: scale(1.05);
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.cab-details {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.cab-type {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
  text-align: center;
}

.cab-info {
  flex: 1;
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 600;
  color: #555;
}

.info-value {
  font-weight: 500;
  color: #2c3e50;
}

.book-btn {
  width: 100%;
  padding: 0.875rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
  margin-top: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .cab-image {
    height: 150px;
  }
  
  .cab-details {
    padding: 1rem;
  }
  
  .cab-type {
    font-size: 1.25rem;
  }
  
  .book-btn {
    padding: 0.75rem;
    font-size: 1rem;
  }
}
