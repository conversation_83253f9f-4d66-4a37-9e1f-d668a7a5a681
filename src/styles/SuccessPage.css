.success-page {
  min-height: calc(100vh - 200px);
  padding: 2rem 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
}

.success-card {
  max-width: 700px;
  margin: 0 auto;
  background: white;
  border-radius: 16px;
  box-shadow: 0 15px 35px rgba(0,0,0,0.1);
  padding: 3rem;
  text-align: center;
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.success-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  animation: bounce 1s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.success-card h1 {
  color: #27ae60;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.success-message {
  font-size: 1.25rem;
  color: #555;
  margin-bottom: 2rem;
  line-height: 1.6;
}

/* Booking Summary */
.booking-summary {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  text-align: left;
}

.booking-summary h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
  font-size: 1.5rem;
}

.detail-grid {
  display: grid;
  gap: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: white;
  border-radius: 8px;
  border-left: 4px solid #3498db;
}

.detail-label {
  font-weight: 600;
  color: #555;
}

.detail-value {
  font-weight: 500;
  color: #2c3e50;
  text-align: right;
}

/* Next Steps */
.next-steps {
  background: #e8f5e8;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  text-align: left;
}

.next-steps h3 {
  color: #27ae60;
  margin-bottom: 1.5rem;
  text-align: center;
  font-size: 1.5rem;
}

.next-steps ul {
  list-style: none;
  padding: 0;
}

.next-steps li {
  padding: 0.75rem 0;
  color: #555;
  font-weight: 500;
  border-bottom: 1px solid #d4edda;
}

.next-steps li:last-child {
  border-bottom: none;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin: 2rem 0;
}

.action-buttons .btn {
  min-width: 150px;
  padding: 0.875rem 1.5rem;
  font-weight: 600;
}

/* Contact Info */
.contact-info {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #eee;
}

.contact-info p {
  color: #666;
  margin-bottom: 1rem;
  font-weight: 500;
}

.contact-details {
  display: flex;
  gap: 2rem;
  justify-content: center;
  flex-wrap: wrap;
}

.contact-details span {
  color: #3498db;
  font-weight: 500;
  padding: 0.5rem 1rem;
  background: #f0f8ff;
  border-radius: 20px;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .success-page {
    padding: 1rem;
  }
  
  .success-card {
    padding: 2rem;
    margin: 0;
  }
  
  .success-card h1 {
    font-size: 2rem;
  }
  
  .success-message {
    font-size: 1.1rem;
  }
  
  .booking-summary,
  .next-steps {
    padding: 1.5rem;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .detail-value {
    text-align: left;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .btn {
    width: 100%;
    min-width: auto;
  }
  
  .contact-details {
    flex-direction: column;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .success-card {
    padding: 1.5rem;
  }
  
  .success-icon {
    font-size: 3rem;
  }
  
  .success-card h1 {
    font-size: 1.75rem;
  }
  
  .booking-summary h3,
  .next-steps h3 {
    font-size: 1.25rem;
  }
}
