.home-page {
  min-height: calc(100vh - 200px);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 0;
  text-align: center;
}

.hero-content h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
}

.hero-content p {
  font-size: 1.25rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
  color: white;
}

/* Vehicles Section */
.vehicles-section {
  padding: 4rem 0;
  background-color: #f8f9fa;
}

.vehicles-section h2 {
  text-align: center;
  margin-bottom: 3rem;
  font-size: 2.5rem;
  color: #2c3e50;
}

.vehicles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.error-banner {
  background-color: #fee;
  border: 1px solid #fcc;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  margin-bottom: 2rem;
}

.error-banner p {
  color: #c33;
  margin-bottom: 1rem;
  font-weight: 500;
}

.no-vehicles {
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.no-vehicles h3 {
  color: #666;
  margin-bottom: 1rem;
}

.no-vehicles p {
  color: #888;
}

/* Features Section */
.features-section {
  padding: 4rem 0;
  background: white;
}

.features-section h2 {
  text-align: center;
  margin-bottom: 3rem;
  font-size: 2.5rem;
  color: #2c3e50;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.feature-card {
  text-align: center;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

/* Loading State */
.loading {
  text-align: center;
  padding: 4rem 0;
}

.loading p {
  margin-top: 1rem;
  color: #666;
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    padding: 2rem 0;
  }
  
  .hero-content h1 {
    font-size: 2rem;
  }
  
  .hero-content p {
    font-size: 1rem;
    padding: 0 1rem;
  }
  
  .vehicles-section {
    padding: 2rem 0;
  }
  
  .vehicles-section h2,
  .features-section h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
  }
  
  .vehicles-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .features-section {
    padding: 2rem 0;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .feature-card {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero-content h1 {
    font-size: 1.75rem;
  }
  
  .vehicles-section h2,
  .features-section h2 {
    font-size: 1.75rem;
  }
  
  .feature-icon {
    font-size: 2.5rem;
  }
}
