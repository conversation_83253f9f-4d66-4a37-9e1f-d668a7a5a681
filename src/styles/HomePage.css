.home-page {
  min-height: calc(100vh - 200px);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 6rem 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 1;
}

.hero-content h1 {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  letter-spacing: -1px;
}

.hero-content p {
  font-size: 1.4rem;
  opacity: 0.95;
  max-width: 700px;
  margin: 0 auto;
  color: white;
  line-height: 1.6;
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

/* Vehicles Section */
.vehicles-section {
  padding: 4rem 0;
  background-color: #f8f9fa;
}

.vehicles-section h2 {
  text-align: center;
  margin-bottom: 3rem;
  font-size: 2.5rem;
  color: #2c3e50;
}

.vehicles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2.5rem;
  margin-top: 2rem;
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-banner {
  background-color: #fee;
  border: 1px solid #fcc;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  margin-bottom: 2rem;
}

.error-banner p {
  color: #c33;
  margin-bottom: 1rem;
  font-weight: 500;
}

.no-vehicles {
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.no-vehicles h3 {
  color: #666;
  margin-bottom: 1rem;
}

.no-vehicles p {
  color: #888;
}

/* Features Section */
.features-section {
  padding: 4rem 0;
  background: white;
}

.features-section h2 {
  text-align: center;
  margin-bottom: 3rem;
  font-size: 2.5rem;
  color: #2c3e50;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2.5rem;
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.feature-card {
  text-align: center;
  padding: 2.5rem 2rem;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0,0,0,0.05);
  box-shadow: 0 8px 25px rgba(0,0,0,0.08);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
  border-color: rgba(52, 152, 219, 0.2);
}

.feature-icon {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  color: #3498db;
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
  transform: scale(1.1);
  color: #2980b9;
}

.feature-card h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.4rem;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.feature-card p {
  color: #666;
  line-height: 1.7;
  font-size: 1rem;
}

/* Loading State */
.loading {
  text-align: center;
  padding: 4rem 0;
}

.loading p {
  margin-top: 1rem;
  color: #666;
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    padding: 2rem 0;
  }
  
  .hero-content h1 {
    font-size: 2rem;
  }
  
  .hero-content p {
    font-size: 1rem;
    padding: 0 1rem;
  }
  
  .vehicles-section {
    padding: 2rem 0;
  }
  
  .vehicles-section h2,
  .features-section h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
  }
  
  .vehicles-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .features-section {
    padding: 2rem 0;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .feature-card {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero-content h1 {
    font-size: 1.75rem;
  }
  
  .vehicles-section h2,
  .features-section h2 {
    font-size: 1.75rem;
  }
  
  .feature-icon {
    font-size: 2.5rem;
  }
}
