// Utility functions for API handling

/**
 * Ensures the response data is an array
 * @param {any} data - The data to check
 * @returns {Array} - Always returns an array
 */
export const ensureArray = (data) => {
  return Array.isArray(data) ? data : [];
};

/**
 * Handles API errors and provides user-friendly messages
 * @param {Error} error - The error object
 * @returns {string} - User-friendly error message
 */
export const getErrorMessage = (error) => {
  if (error.response) {
    // Server responded with error status
    const status = error.response.status;
    const message = error.response.data?.message || error.response.data?.error;
    
    switch (status) {
      case 400:
        return message || 'Invalid request. Please check your input.';
      case 401:
        return 'Unauthorized. Please log in again.';
      case 403:
        return 'Access denied. You don\'t have permission for this action.';
      case 404:
        return 'Resource not found.';
      case 500:
        return 'Server error. Please try again later.';
      default:
        return message || `Error ${status}: Something went wrong.`;
    }
  } else if (error.request) {
    // Network error
    return 'Network error. Please check your internet connection.';
  } else {
    // Other error
    return error.message || 'An unexpected error occurred.';
  }
};

/**
 * Creates a toast notification (can be extended with a toast library)
 * @param {string} message - The message to display
 * @param {string} type - The type of toast ('success', 'error', 'info')
 */
export const showToast = (message, type = 'info') => {
  // For now, just use alert - can be replaced with a proper toast library
  if (type === 'error') {
    alert(`Error: ${message}`);
  } else if (type === 'success') {
    alert(`Success: ${message}`);
  } else {
    alert(message);
  }
};

/**
 * Formats date for display
 * @param {string} dateString - ISO date string
 * @returns {string} - Formatted date
 */
export const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

/**
 * Gets the correct ID from a booking object (handles both _id and id)
 * @param {Object} booking - Booking object
 * @returns {string} - The booking ID
 */
export const getBookingId = (booking) => {
  if (!booking) {
    console.error('No booking object provided to getBookingId');
    return null;
  }

  const id = booking._id || booking.id;
  if (!id) {
    console.error('No valid ID found in booking object:', booking);
  }

  return id;
};

/**
 * Validates if a string is a valid MongoDB ObjectId
 * @param {string} id - The ID to validate
 * @returns {boolean} - True if valid ObjectId format
 */
export const isValidObjectId = (id) => {
  return /^[0-9a-fA-F]{24}$/.test(id);
};

/**
 * Formats time for display
 * @param {string} timeString - Time string in HH:MM format
 * @returns {string} - Formatted time
 */
export const formatTime = (timeString) => {
  return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-IN', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
};

/**
 * Validates phone number
 * @param {string} phone - Phone number to validate
 * @returns {boolean} - True if valid
 */
export const isValidPhone = (phone) => {
  const phoneRegex = /^\d{10}$/;
  return phoneRegex.test(phone.replace(/\D/g, ''));
};

/**
 * Validates email address
 * @param {string} email - Email to validate
 * @returns {boolean} - True if valid
 */
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Gets minimum date for date inputs (today)
 * @returns {string} - Date string in YYYY-MM-DD format
 */
export const getMinDate = () => {
  const today = new Date();
  return today.toISOString().split('T')[0];
};
