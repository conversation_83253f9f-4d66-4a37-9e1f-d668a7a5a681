import axios from 'axios';
import { ensureArray, getErrorMessage } from '../utils/apiHelpers';

const API_BASE_URL = 'http://localhost:3001/api'; // Adjust this to your backend URL

// Get all vehicles
export const getVehicles = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/vehicles`);
    // Handle backend response structure: { success: true, count: number, data: array }
    const vehicles = response.data.data || response.data;
    return ensureArray(vehicles);
  } catch (error) {
    console.error('Error fetching vehicles:', error);
    throw new Error(getErrorMessage(error));
  }
};

// Add a new vehicle (Admin)
export const addVehicle = async (vehicleData) => {
  try {
    const formData = new FormData();
    
    // Append all vehicle data to FormData
    Object.keys(vehicleData).forEach(key => {
      if (vehicleData[key] !== null && vehicleData[key] !== undefined) {
        formData.append(key, vehicleData[key]);
      }
    });

    const response = await axios.post(`${API_BASE_URL}/vehicles`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error adding vehicle:', error);
    throw error;
  }
};

// Update a vehicle (Admin)
export const updateVehicle = async (id, vehicleData) => {
  try {
    const formData = new FormData();
    
    // Append all vehicle data to FormData
    Object.keys(vehicleData).forEach(key => {
      if (vehicleData[key] !== null && vehicleData[key] !== undefined) {
        formData.append(key, vehicleData[key]);
      }
    });

    const response = await axios.put(`${API_BASE_URL}/vehicles/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error updating vehicle:', error);
    throw error;
  }
};

// Delete a vehicle (Admin)
export const deleteVehicle = async (id) => {
  try {
    const response = await axios.delete(`${API_BASE_URL}/vehicles/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting vehicle:', error);
    throw error;
  }
};
