import axios from 'axios';
import { ensureArray, getErrorMessage } from '../utils/apiHelpers';

const API_BASE_URL = 'http://localhost:3001/api'; // Adjust this to your backend URL

// Create a new booking
export const createBooking = async (bookingData) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/bookings`, bookingData, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error creating booking:', error);
    throw error;
  }
};

// Get all bookings (Admin)
export const getBookings = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/bookings`);
    // Handle backend response structure: { success: true, count: number, data: array }
    const bookings = response.data.data || response.data;
    return ensureArray(bookings);
  } catch (error) {
    console.error('Error fetching bookings:', error);
    throw new Error(getErrorMessage(error));
  }
};

// Get a specific booking by ID
export const getBookingById = async (id) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/bookings/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching booking:', error);
    throw error;
  }
};

// Update booking status (Admin)
export const updateBookingStatus = async (id, status) => {
  try {
    const response = await axios.patch(`${API_BASE_URL}/bookings/${id}`, { status }, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error updating booking status:', error);
    throw error;
  }
};

// Delete a booking (Admin)
export const deleteBooking = async (id) => {
  try {
    const response = await axios.delete(`${API_BASE_URL}/bookings/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting booking:', error);
    throw error;
  }
};
