import axios from 'axios';
import { ensureArray, getErrorMessage } from '../utils/apiHelpers';

const API_BASE_URL = 'http://localhost:3001/api'; // Adjust this to your backend URL

// Create a new booking
export const createBooking = async (bookingData) => {
  try {
    // Validate required fields
    const requiredFields = ['name', 'phone', 'pickup', 'drop', 'date', 'time', 'vehicleType'];
    const missingFields = requiredFields.filter(field => !bookingData[field]);

    if (missingFields.length > 0) {
      throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
    }

    // Validate date is not in the past
    const bookingDateTime = new Date(`${bookingData.date}T${bookingData.time}`);
    if (bookingDateTime < new Date()) {
      throw new Error('Booking date and time cannot be in the past');
    }

    const response = await axios.post(`${API_BASE_URL}/bookings`, bookingData, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error creating booking:', error);
    throw error;
  }
};

// Get all bookings with optional filters (Admin)
export const getBookings = async (filters = {}) => {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();

    // Add filters if provided
    if (filters.status) queryParams.append('status', filters.status);
    if (filters.date) queryParams.append('date', filters.date);
    if (filters.vehicleType) queryParams.append('vehicleType', filters.vehicleType);
    if (filters.customerId) queryParams.append('customerId', filters.customerId);

    const url = `${API_BASE_URL}/bookings${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await axios.get(url);

    // Handle backend response structure: { success: true, count: number, data: array }
    const bookings = response.data.data || response.data;
    return ensureArray(bookings);
  } catch (error) {
    console.error('Error fetching bookings:', error);
    throw new Error(getErrorMessage(error));
  }
};

// Get bookings by status
export const getBookingsByStatus = async (status) => {
  const validStatuses = ['pending', 'confirmed', 'completed', 'cancelled'];
  if (!validStatuses.includes(status)) {
    throw new Error(`Invalid status. Must be one of: ${validStatuses.join(', ')}`);
  }
  return getBookings({ status });
};

// Get today's bookings
export const getTodaysBookings = async () => {
  const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
  return getBookings({ date: today });
};

// Get bookings by vehicle type
export const getBookingsByVehicleType = async (vehicleType) => {
  return getBookings({ vehicleType });
};

// Get a specific booking by ID
export const getBookingById = async (id) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/bookings/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching booking:', error);
    throw error;
  }
};

// Update booking status (Admin) - Using the correct endpoint from the API guide
export const updateBookingStatus = async (id, status) => {
  try {
    // Validate status
    const validStatuses = ['pending', 'confirmed', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      throw new Error(`Invalid status. Must be one of: ${validStatuses.join(', ')}`);
    }

    const response = await axios.put(`${API_BASE_URL}/bookings/${id}/status`, { status }, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error updating booking status:', error);
    throw error;
  }
};

// Convenience methods for specific status updates
export const confirmBooking = async (id) => {
  return updateBookingStatus(id, 'confirmed');
};

export const completeBooking = async (id) => {
  return updateBookingStatus(id, 'completed');
};

export const cancelBooking = async (id) => {
  return updateBookingStatus(id, 'cancelled');
};

export const resetBookingToPending = async (id) => {
  return updateBookingStatus(id, 'pending');
};

// Update booking details (Admin)
export const updateBooking = async (id, bookingData) => {
  try {
    const response = await axios.put(`${API_BASE_URL}/bookings/${id}`, bookingData, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error updating booking:', error);
    throw error;
  }
};

// Delete a booking (Admin)
export const deleteBooking = async (id) => {
  try {
    const response = await axios.delete(`${API_BASE_URL}/bookings/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting booking:', error);
    throw error;
  }
};

// Get booking statistics
export const getBookingStats = async () => {
  try {
    const allBookings = await getBookings();

    const stats = {
      total: allBookings.length,
      pending: allBookings.filter(b => b.status === 'pending').length,
      confirmed: allBookings.filter(b => b.status === 'confirmed').length,
      completed: allBookings.filter(b => b.status === 'completed').length,
      cancelled: allBookings.filter(b => b.status === 'cancelled').length,
    };

    // Calculate revenue from completed bookings
    const completedBookings = allBookings.filter(b => b.status === 'completed');
    stats.totalRevenue = completedBookings.reduce((sum, booking) => {
      return sum + (booking.estimatedPrice || 0);
    }, 0);

    // Today's bookings count
    const today = new Date().toISOString().split('T')[0];
    stats.todaysBookings = allBookings.filter(b => b.date === today).length;

    return stats;
  } catch (error) {
    console.error('Error fetching booking statistics:', error);
    throw error;
  }
};
