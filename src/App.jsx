import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTaxi, faHome, faUserShield } from '@fortawesome/free-solid-svg-icons';
import HomePage from './pages/HomePage';
import SuccessPage from './pages/SuccessPage';
import AdminPage from './pages/AdminPage';
import BookingForm from './components/BookingForm';
import BookingAPITest from './components/BookingAPITest';
import './App.css';

function App() {
  return (
    <Router>
      <div className="App">
        <nav className="navbar">
          <div className="nav-container">
            <Link to="/" className="nav-logo">
              <FontAwesomeIcon icon={faTaxi} className="nav-logo-icon" />
              ZAF Holidays Transport
            </Link>
            <div className="nav-menu">
              <Link to="/" className="nav-link">
                <FontAwesomeIcon icon={faHome} className="nav-link-icon" />
                Home
              </Link>
              <Link to="/admin" className="nav-link">
                <FontAwesomeIcon icon={faUserShield} className="nav-link-icon" />
                Admin
              </Link>
              <Link to="/api-test" className="nav-link">
                🧪 API Test
              </Link>
            </div>
          </div>
        </nav>

        <main className="main-content">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/booking" element={<BookingForm />} />
            <Route path="/success" element={<SuccessPage />} />
            <Route path="/admin" element={<AdminPage />} />
            <Route path="/api-test" element={<BookingAPITest />} />
          </Routes>
        </main>

        <footer className="footer">
          <div className="footer-content">
            <p>&copy; 2024 ZAF Holidays Transport. All rights reserved.</p>
          </div>
        </footer>
      </div>
    </Router>
  );
}

export default App;
