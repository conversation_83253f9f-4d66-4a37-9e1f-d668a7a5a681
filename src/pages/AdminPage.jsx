import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faClipboardList,
  faCar,
  faMapMarkerAlt,
  faBullseye,
  faUsers,
  faDollarSign
} from '@fortawesome/free-solid-svg-icons';
import { getVehicles, deleteVehicle } from '../api/vehicles';
import {
  getBookings,
  getBookingsByStatus,
  getTodaysBookings,
  updateBookingStatus,
  deleteBooking,
  getBookingStats,
  confirmBooking,
  completeBooking,
  cancelBooking
} from '../api/bookings';
import CabFormAdmin from '../components/CabFormAdmin';
import '../styles/AdminPage.css';

const AdminPage = () => {
  const [activeTab, setActiveTab] = useState('bookings');
  const [vehicles, setVehicles] = useState([]);
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showVehicleForm, setShowVehicleForm] = useState(false);
  const [editingVehicle, setEditingVehicle] = useState(null);

  // New state for booking filters and stats
  const [bookingFilter, setBookingFilter] = useState('all');
  const [bookingStats, setBookingStats] = useState(null);
  const [dateFilter, setDateFilter] = useState('');

  useEffect(() => {
    if (activeTab === 'bookings') {
      fetchBookings();
      fetchBookingStats();
    } else if (activeTab === 'vehicles') {
      fetchVehicles();
    }
  }, [activeTab, bookingFilter, dateFilter]);

  const fetchVehicles = async () => {
    try {
      setLoading(true);
      const data = await getVehicles();
      setVehicles(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching vehicles:', error);
      // Set mock data for development
      setVehicles([
        {
          id: 1,
          type: 'Sedan',
          seats: 4,
          pricePerKm: 12,
          description: 'Comfortable sedan with AC and music system',
          imageUrl: null
        },
        {
          id: 2,
          type: 'SUV',
          seats: 7,
          pricePerKm: 18,
          description: 'Spacious SUV perfect for family trips',
          imageUrl: null
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const fetchBookings = async (filters = {}) => {
    try {
      setLoading(true);
      let data;

      // Apply filters
      if (bookingFilter === 'today') {
        data = await getTodaysBookings();
      } else if (bookingFilter !== 'all') {
        data = await getBookingsByStatus(bookingFilter);
      } else {
        // Build filter object
        const filterObj = {};
        if (dateFilter) filterObj.date = dateFilter;
        data = await getBookings(filterObj);
      }

      setBookings(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching bookings:', error);
      // Set mock data for development
      setBookings([
        {
          id: 1,
          name: 'John Doe',
          phone: '9876543210',
          email: '<EMAIL>',
          pickup: 'Airport',
          drop: 'Hotel Grand',
          date: '2024-08-05',
          time: '10:00',
          vehicleType: 'Sedan',
          status: 'pending',
          distance: 15,
          notes: 'Please call upon arrival',
          estimatedPrice: 225,
          createdAt: '2024-08-04T10:30:00Z'
        },
        {
          id: 2,
          name: 'Jane Smith',
          phone: '9876543211',
          email: '<EMAIL>',
          pickup: 'Railway Station',
          drop: 'City Center',
          date: '2024-08-06',
          time: '14:30',
          vehicleType: 'SUV',
          status: 'confirmed',
          distance: 8,
          estimatedPrice: 144,
          createdAt: '2024-08-04T11:15:00Z'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const fetchBookingStats = async () => {
    try {
      const stats = await getBookingStats();
      setBookingStats(stats);
    } catch (error) {
      console.error('Error fetching booking stats:', error);
      // Set mock stats for development
      setBookingStats({
        total: 25,
        pending: 5,
        confirmed: 8,
        completed: 10,
        cancelled: 2,
        totalRevenue: 5250,
        todaysBookings: 3
      });
    }
  };

  const handleDeleteVehicle = async (id) => {
    if (window.confirm('Are you sure you want to delete this vehicle?')) {
      try {
        await deleteVehicle(id);
        fetchVehicles();
      } catch (error) {
        console.error('Error deleting vehicle:', error);
        alert('Failed to delete vehicle. Please try again.');
      }
    }
  };

  const handleDeleteBooking = async (id) => {
    if (window.confirm('Are you sure you want to delete this booking? This action cannot be undone.')) {
      try {
        await deleteBooking(id);
        fetchBookings();
        fetchBookingStats();
      } catch (error) {
        console.error('Error deleting booking:', error);
        alert('Failed to delete booking. Please try again.');
      }
    }
  };

  const handleUpdateBookingStatus = async (id, status) => {
    try {
      await updateBookingStatus(id, status);
      fetchBookings();
      fetchBookingStats(); // Refresh stats after status update
    } catch (error) {
      console.error('Error updating booking status:', error);
      alert('Failed to update booking status. Please try again.');
    }
  };

  // Quick action methods
  const handleQuickConfirm = async (id) => {
    try {
      await confirmBooking(id);
      fetchBookings();
      fetchBookingStats();
    } catch (error) {
      console.error('Error confirming booking:', error);
      alert('Failed to confirm booking. Please try again.');
    }
  };

  const handleQuickComplete = async (id) => {
    try {
      await completeBooking(id);
      fetchBookings();
      fetchBookingStats();
    } catch (error) {
      console.error('Error completing booking:', error);
      alert('Failed to complete booking. Please try again.');
    }
  };

  const handleQuickCancel = async (id) => {
    if (window.confirm('Are you sure you want to cancel this booking?')) {
      try {
        await cancelBooking(id);
        fetchBookings();
        fetchBookingStats();
      } catch (error) {
        console.error('Error cancelling booking:', error);
        alert('Failed to cancel booking. Please try again.');
      }
    }
  };

  const handleVehicleFormSave = () => {
    setShowVehicleForm(false);
    setEditingVehicle(null);
    fetchVehicles();
  };

  const handleVehicleFormCancel = () => {
    setShowVehicleForm(false);
    setEditingVehicle(null);
  };

  const handleEditVehicle = (vehicle) => {
    setEditingVehicle(vehicle);
    setShowVehicleForm(true);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatTime = (timeString) => {
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-IN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  return (
    <div className="admin-page">
      <div className="container">
        <div className="admin-header">
          <h1>Admin Dashboard</h1>
          <p>Manage your bookings and vehicle fleet</p>
        </div>

        <div className="admin-tabs">
          <button
            className={`tab-button ${activeTab === 'bookings' ? 'active' : ''}`}
            onClick={() => setActiveTab('bookings')}
          >
            <FontAwesomeIcon icon={faClipboardList} className="tab-icon" />
            Bookings
          </button>
          <button
            className={`tab-button ${activeTab === 'vehicles' ? 'active' : ''}`}
            onClick={() => setActiveTab('vehicles')}
          >
            <FontAwesomeIcon icon={faCar} className="tab-icon" />
            Vehicles
          </button>
        </div>

        <div className="admin-content">
          {activeTab === 'bookings' && (
            <div className="bookings-section">
              {/* Booking Statistics Dashboard */}
              {bookingStats && (
                <div className="stats-dashboard">
                  <div className="stats-grid">
                    <div className="stat-card">
                      <h3>{bookingStats.total}</h3>
                      <p>Total Bookings</p>
                    </div>
                    <div className="stat-card pending">
                      <h3>{bookingStats.pending}</h3>
                      <p>Pending</p>
                    </div>
                    <div className="stat-card confirmed">
                      <h3>{bookingStats.confirmed}</h3>
                      <p>Confirmed</p>
                    </div>
                    <div className="stat-card completed">
                      <h3>{bookingStats.completed}</h3>
                      <p>Completed</p>
                    </div>
                    <div className="stat-card cancelled">
                      <h3>{bookingStats.cancelled}</h3>
                      <p>Cancelled</p>
                    </div>
                    <div className="stat-card revenue">
                      <h3>₹{bookingStats.totalRevenue}</h3>
                      <p>Total Revenue</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Filters */}
              <div className="booking-filters">
                <div className="filter-group">
                  <label htmlFor="statusFilter">Filter by Status:</label>
                  <select
                    id="statusFilter"
                    value={bookingFilter}
                    onChange={(e) => setBookingFilter(e.target.value)}
                    className="filter-select"
                  >
                    <option value="all">All Bookings</option>
                    <option value="pending">Pending</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                    <option value="today">Today's Bookings</option>
                  </select>
                </div>

                <div className="filter-group">
                  <label htmlFor="dateFilter">Filter by Date:</label>
                  <input
                    type="date"
                    id="dateFilter"
                    value={dateFilter}
                    onChange={(e) => setDateFilter(e.target.value)}
                    className="filter-input"
                  />
                </div>

                <button
                  onClick={() => {
                    setBookingFilter('all');
                    setDateFilter('');
                  }}
                  className="btn btn-secondary btn-sm"
                >
                  Clear Filters
                </button>
              </div>

              <div className="section-header">
                <h2>
                  {bookingFilter === 'all' ? 'All Bookings' :
                   bookingFilter === 'today' ? "Today's Bookings" :
                   `${bookingFilter.charAt(0).toUpperCase() + bookingFilter.slice(1)} Bookings`}
                </h2>
                <span className="count-badge">{Array.isArray(bookings) ? bookings.length : 0} found</span>
              </div>

              {loading ? (
                <div className="loading">
                  <div className="spinner"></div>
                  <p>Loading bookings...</p>
                </div>
              ) : (!Array.isArray(bookings) || bookings.length === 0) ? (
                <div className="empty-state">
                  <h3>No bookings found</h3>
                  <p>Bookings will appear here when customers make reservations.</p>
                </div>
              ) : (
                <div className="bookings-table-container">
                  <table className="bookings-table">
                    <thead>
                      <tr>
                        <th>Customer</th>
                        <th>Journey</th>
                        <th>Date & Time</th>
                        <th>Vehicle</th>
                        <th>Price</th>
                        <th>Status</th>
                        <th>Quick Actions</th>
                        <th>More</th>
                      </tr>
                    </thead>
                    <tbody>
                      {Array.isArray(bookings) && bookings.map(booking => (
                        <tr key={booking.id}>
                          <td>
                            <div className="customer-info">
                              <strong>{booking.name}</strong>
                              <span>{booking.phone}</span>
                              {booking.email && <span className="email">{booking.email}</span>}
                            </div>
                          </td>
                          <td>
                            <div className="journey-info">
                              <div>
                                <FontAwesomeIcon icon={faMapMarkerAlt} className="journey-icon" />
                                {booking.pickup}
                              </div>
                              <div>
                                <FontAwesomeIcon icon={faBullseye} className="journey-icon" />
                                {booking.drop}
                              </div>
                              {booking.distance && (
                                <div className="distance-info">
                                  {booking.distance} km
                                </div>
                              )}
                            </div>
                          </td>
                          <td>
                            <div className="datetime-info">
                              <div>{formatDate(booking.date)}</div>
                              <div>{formatTime(booking.time)}</div>
                            </div>
                          </td>
                          <td>
                            <span className="vehicle-type">{booking.vehicleType}</span>
                          </td>
                          <td>
                            <div className="price-info">
                              {booking.estimatedPrice ? (
                                <span className="price">₹{booking.estimatedPrice}</span>
                              ) : (
                                <span className="no-price">-</span>
                              )}
                            </div>
                          </td>
                          <td>
                            <select
                              value={booking.status}
                              onChange={(e) => handleUpdateBookingStatus(booking.id, e.target.value)}
                              className={`status-select status-${booking.status}`}
                            >
                              <option value="pending">Pending</option>
                              <option value="confirmed">Confirmed</option>
                              <option value="completed">Completed</option>
                              <option value="cancelled">Cancelled</option>
                            </select>
                          </td>
                          <td>
                            <div className="quick-actions">
                              {booking.status === 'pending' && (
                                <button
                                  onClick={() => handleQuickConfirm(booking.id)}
                                  className="btn btn-success btn-xs"
                                  title="Confirm Booking"
                                >
                                  ✓
                                </button>
                              )}
                              {booking.status === 'confirmed' && (
                                <button
                                  onClick={() => handleQuickComplete(booking.id)}
                                  className="btn btn-primary btn-xs"
                                  title="Complete Booking"
                                >
                                  ✓✓
                                </button>
                              )}
                              {(booking.status === 'pending' || booking.status === 'confirmed') && (
                                <button
                                  onClick={() => handleQuickCancel(booking.id)}
                                  className="btn btn-warning btn-xs"
                                  title="Cancel Booking"
                                >
                                  ✗
                                </button>
                              )}
                            </div>
                          </td>
                          <td>
                            <div className="more-actions">
                              <button
                                onClick={() => handleDeleteBooking(booking.id)}
                                className="btn btn-danger btn-sm"
                                title="Delete Booking"
                              >
                                Delete
                              </button>
                              {booking.notes && (
                                <div className="notes-indicator" title={booking.notes}>
                                  📝
                                </div>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}

          {activeTab === 'vehicles' && (
            <div className="vehicles-section">
              <div className="section-header">
                <h2>Vehicle Management</h2>
                <button
                  onClick={() => setShowVehicleForm(true)}
                  className="btn btn-primary"
                >
                  + Add New Vehicle
                </button>
              </div>

              {showVehicleForm && (
                <CabFormAdmin
                  vehicle={editingVehicle}
                  onSave={handleVehicleFormSave}
                  onCancel={handleVehicleFormCancel}
                  isEditing={!!editingVehicle}
                />
              )}

              {loading ? (
                <div className="loading">
                  <div className="spinner"></div>
                  <p>Loading vehicles...</p>
                </div>
              ) : (!Array.isArray(vehicles) || vehicles.length === 0) ? (
                <div className="empty-state">
                  <h3>No vehicles found</h3>
                  <p>Add your first vehicle to get started.</p>
                </div>
              ) : (
                <div className="vehicles-grid">
                  {Array.isArray(vehicles) && vehicles.map(vehicle => (
                    <div key={vehicle._id || vehicle.id} className="vehicle-card">
                      <div className="vehicle-image">
                        {vehicle.imageUrl ? (
                          <img
                            src={vehicle.imageUrl.startsWith('http') ? vehicle.imageUrl : `http://localhost:3001${vehicle.imageUrl}`}
                            alt={vehicle.type}
                          />
                        ) : (
                          <div className="image-placeholder">
                            <FontAwesomeIcon icon={faCar} className="placeholder-icon" />
                          </div>
                        )}
                      </div>
                      
                      <div className="vehicle-details">
                        <h3>{vehicle.type}</h3>
                        <div className="vehicle-specs">
                          <span>
                            <FontAwesomeIcon icon={faUsers} className="spec-icon" />
                            {vehicle.seats} seats
                          </span>
                          <span>
                            <FontAwesomeIcon icon={faDollarSign} className="spec-icon" />
                            ₹{vehicle.pricePerKm}/km
                          </span>
                        </div>
                        {vehicle.description && (
                          <p className="vehicle-features">{vehicle.description}</p>
                        )}
                        
                        <div className="vehicle-actions">
                          <button
                            onClick={() => handleEditVehicle(vehicle)}
                            className="btn btn-secondary btn-sm"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDeleteVehicle(vehicle._id || vehicle.id)}
                            className="btn btn-danger btn-sm"
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminPage;
