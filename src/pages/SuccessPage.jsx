import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import '../styles/SuccessPage.css';

const SuccessPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  const bookingDetails = location.state?.bookingDetails;

  const handleBackToHome = () => {
    navigate('/');
  };

  const handleBookAnother = () => {
    navigate('/');
  };

  return (
    <div className="success-page">
      <div className="container">
        <div className="success-card">
          <div className="success-icon">
            ✅
          </div>
          
          <h1>Booking Confirmed!</h1>
          <p className="success-message">
            Thank you! Your cab has been successfully booked.
          </p>
          
          {bookingDetails && (
            <div className="booking-summary">
              <h3>Booking Details</h3>
              <div className="detail-grid">
                <div className="detail-item">
                  <span className="detail-label">Name:</span>
                  <span className="detail-value">{bookingDetails.name}</span>
                </div>
                
                <div className="detail-item">
                  <span className="detail-label">Phone:</span>
                  <span className="detail-value">{bookingDetails.phone}</span>
                </div>
                
                <div className="detail-item">
                  <span className="detail-label">Pickup:</span>
                  <span className="detail-value">{bookingDetails.pickup}</span>
                </div>

                <div className="detail-item">
                  <span className="detail-label">Drop:</span>
                  <span className="detail-value">{bookingDetails.drop}</span>
                </div>
                
                <div className="detail-item">
                  <span className="detail-label">Date:</span>
                  <span className="detail-value">
                    {new Date(bookingDetails.date).toLocaleDateString('en-IN', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </span>
                </div>
                
                <div className="detail-item">
                  <span className="detail-label">Time:</span>
                  <span className="detail-value">
                    {new Date(`2000-01-01T${bookingDetails.time}`).toLocaleTimeString('en-IN', {
                      hour: '2-digit',
                      minute: '2-digit',
                      hour12: true
                    })}
                  </span>
                </div>
                
                <div className="detail-item">
                  <span className="detail-label">Vehicle:</span>
                  <span className="detail-value">{bookingDetails.vehicleType}</span>
                </div>
              </div>
            </div>
          )}
          
          <div className="next-steps">
            <h3>What's Next?</h3>
            <ul>
              <li>📞 Our team will contact you shortly to confirm the booking</li>
              <li>🚗 Your driver will arrive at the pickup location on time</li>
              <li>💬 You'll receive SMS updates about your trip</li>
              <li>📧 A confirmation email has been sent to you</li>
            </ul>
          </div>
          
          <div className="action-buttons">
            <button 
              onClick={handleBackToHome}
              className="btn btn-secondary"
            >
              Back to Home
            </button>
            
            <button 
              onClick={handleBookAnother}
              className="btn btn-primary"
            >
              Book Another Cab
            </button>
          </div>
          
          <div className="contact-info">
            <p>Need help? Contact us at:</p>
            <div className="contact-details">
              <span>📞 +91 98765 43210</span>
              <span>✉️ <EMAIL></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuccessPage;
