import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCar, faUserTie, faDollarSign, faMobileAlt } from '@fortawesome/free-solid-svg-icons';
import { getVehicles } from '../api/vehicles';
import CabCard from '../components/CabCard';
import '../styles/HomePage.css';

const HomePage = () => {
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchVehicles();
  }, []);

  const fetchVehicles = async () => {
    try {
      setLoading(true);
      const data = await getVehicles();
      // Ensure data is an array
      setVehicles(Array.isArray(data) ? data : []);
      setError(null);
    } catch (error) {
      console.error('Error fetching vehicles:', error);
      setError('Failed to load vehicles. Please try again later.');
      // Set some mock data for development
      setVehicles([
        {
          id: 1,
          type: 'Sedan',
          seats: 4,
          pricePerKm: 12,
          description: 'Comfortable sedan with AC and music system',
          imageUrl: null
        },
        {
          id: 2,
          type: 'SUV',
          seats: 7,
          pricePerKm: 18,
          description: 'Spacious SUV perfect for family trips',
          imageUrl: null
        },
        {
          id: 3,
          type: 'Hatchback',
          seats: 4,
          pricePerKm: 10,
          description: 'Fuel efficient hatchback for city rides',
          imageUrl: null
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="home-page">
        <div className="container">
          <div className="loading">
            <div className="spinner"></div>
            <p>Loading available vehicles...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="home-page">
      <div className="hero-section">
        <div className="container">
          <div className="hero-content">
            <h1>Book Your Perfect Ride</h1>
            <p>Choose from our fleet of comfortable and reliable vehicles for your journey</p>
          </div>
        </div>
      </div>

      <div className="vehicles-section">
        <div className="container">
          <h2>Available Vehicles</h2>
          
          {error && (
            <div className="error-banner">
              <p>{error}</p>
              <button 
                onClick={fetchVehicles}
                className="btn btn-primary"
              >
                Retry
              </button>
            </div>
          )}
          
          {(!Array.isArray(vehicles) || vehicles.length === 0) && !error ? (
            <div className="no-vehicles">
              <h3>No vehicles available</h3>
              <p>Please check back later or contact us for assistance.</p>
            </div>
          ) : (
            <div className="vehicles-grid">
              {Array.isArray(vehicles) && vehicles.map(vehicle => (
                <CabCard key={vehicle.id} vehicle={vehicle} />
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="features-section">
        <div className="container">
          <h2>Why Choose Us?</h2>
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">
                <FontAwesomeIcon icon={faCar} />
              </div>
              <h3>Quality Vehicles</h3>
              <p>Well-maintained and comfortable vehicles for your journey</p>
            </div>

            <div className="feature-card">
              <div className="feature-icon">
                <FontAwesomeIcon icon={faUserTie} />
              </div>
              <h3>Professional Drivers</h3>
              <p>Experienced and courteous drivers for a safe trip</p>
            </div>

            <div className="feature-card">
              <div className="feature-icon">
                <FontAwesomeIcon icon={faDollarSign} />
              </div>
              <h3>Affordable Rates</h3>
              <p>Competitive pricing with no hidden charges</p>
            </div>

            <div className="feature-card">
              <div className="feature-icon">
                <FontAwesomeIcon icon={faMobileAlt} />
              </div>
              <h3>Easy Booking</h3>
              <p>Simple and quick online booking process</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
