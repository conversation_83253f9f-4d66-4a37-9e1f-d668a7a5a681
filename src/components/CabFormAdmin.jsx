import React, { useState, useEffect } from 'react';
import { addVehicle, updateVehicle } from '../api/vehicles';
import '../styles/CabFormAdmin.css';

const CabFormAdmin = ({ vehicle, onSave, onCancel, isEditing = false }) => {
  const [formData, setFormData] = useState({
    type: '',
    seats: '',
    pricePerKm: '',
    description: '',
    image: null
  });
  
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [imagePreview, setImagePreview] = useState(null);

  useEffect(() => {
    if (isEditing && vehicle) {
      setFormData({
        type: vehicle.type || '',
        seats: vehicle.seats || '',
        pricePerKm: vehicle.pricePerKm || '',
        description: vehicle.description || '',
        image: null
      });

      if (vehicle.imageUrl) {
        const imageUrl = vehicle.imageUrl.startsWith('http') ? vehicle.imageUrl : `http://localhost:3001${vehicle.imageUrl}`;
        setImagePreview(imageUrl);
      }
    }
  }, [isEditing, vehicle]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData(prev => ({
        ...prev,
        image: file
      }));
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
      
      // Clear error
      if (errors.image) {
        setErrors(prev => ({
          ...prev,
          image: ''
        }));
      }
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.type.trim()) {
      newErrors.type = 'Vehicle type is required';
    }
    
    if (!formData.seats || formData.seats < 1) {
      newErrors.seats = 'Number of seats must be at least 1';
    }
    
    if (!formData.pricePerKm || formData.pricePerKm < 0) {
      newErrors.pricePerKm = 'Price per km must be a positive number';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    
    try {
      const submitData = {
        ...formData,
        seats: parseInt(formData.seats),
        pricePerKm: parseFloat(formData.pricePerKm)
      };
      
      if (isEditing) {
        await updateVehicle(vehicle._id || vehicle.id, submitData);
      } else {
        await addVehicle(submitData);
      }
      
      onSave();
    } catch (error) {
      console.error('Error saving vehicle:', error);
      alert(`Failed to ${isEditing ? 'update' : 'add'} vehicle. Please try again.`);
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    setFormData({
      type: '',
      seats: '',
      pricePerKm: '',
      description: '',
      image: null
    });
    setImagePreview(null);
    setErrors({});
  };

  return (
    <div className="cab-form-admin">
      <div className="form-header">
        <h3>{isEditing ? 'Edit Vehicle' : 'Add New Vehicle'}</h3>
      </div>
      
      <form onSubmit={handleSubmit} className="admin-form">
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="type" className="form-label">Vehicle Type *</label>
            <input
              type="text"
              id="type"
              name="type"
              value={formData.type}
              onChange={handleInputChange}
              className={`form-control ${errors.type ? 'error' : ''}`}
              placeholder="e.g., Sedan, SUV, Hatchback"
            />
            {errors.type && <div className="error-message">{errors.type}</div>}
          </div>
          
          <div className="form-group">
            <label htmlFor="seats" className="form-label">Number of Seats *</label>
            <input
              type="number"
              id="seats"
              name="seats"
              value={formData.seats}
              onChange={handleInputChange}
              min="1"
              max="20"
              className={`form-control ${errors.seats ? 'error' : ''}`}
              placeholder="e.g., 4"
            />
            {errors.seats && <div className="error-message">{errors.seats}</div>}
          </div>
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="pricePerKm" className="form-label">Price per KM (₹) *</label>
            <input
              type="number"
              id="pricePerKm"
              name="pricePerKm"
              value={formData.pricePerKm}
              onChange={handleInputChange}
              min="0"
              step="0.01"
              className={`form-control ${errors.pricePerKm ? 'error' : ''}`}
              placeholder="e.g., 12.50"
            />
            {errors.pricePerKm && <div className="error-message">{errors.pricePerKm}</div>}
          </div>
          
          <div className="form-group">
            <label htmlFor="description" className="form-label">Description</label>
            <input
              type="text"
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              className="form-control"
              placeholder="e.g., Comfortable SUV for family trips"
            />
          </div>
        </div>
        
        <div className="form-group">
          <label htmlFor="image" className="form-label">Vehicle Image</label>
          <input
            type="file"
            id="image"
            name="image"
            onChange={handleImageChange}
            accept="image/*"
            className={`form-control ${errors.image ? 'error' : ''}`}
          />
          {errors.image && <div className="error-message">{errors.image}</div>}
          
          {imagePreview && (
            <div className="image-preview">
              <img src={imagePreview} alt="Preview" />
            </div>
          )}
        </div>
        
        <div className="form-actions">
          <button
            type="button"
            onClick={onCancel}
            className="btn btn-secondary"
          >
            Cancel
          </button>
          
          {!isEditing && (
            <button
              type="button"
              onClick={handleReset}
              className="btn btn-secondary"
            >
              Reset
            </button>
          )}
          
          <button
            type="submit"
            disabled={loading}
            className="btn btn-primary"
          >
            {loading ? 'Saving...' : (isEditing ? 'Update Vehicle' : 'Add Vehicle')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CabFormAdmin;
