import React from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/CabCard.css';

const CabCard = ({ vehicle }) => {
  const navigate = useNavigate();

  const handleBookNow = () => {
    navigate('/booking', {
      state: {
        vehicleType: vehicle.type,
        vehicleId: vehicle._id || vehicle.id
      }
    });
  };

  return (
    <div className="cab-card">
      <div className="cab-image">
        {vehicle.imageUrl ? (
          <img
            src={vehicle.imageUrl.startsWith('http') ? vehicle.imageUrl : `http://localhost:3001${vehicle.imageUrl}`}
            alt={vehicle.type}
            onError={(e) => {
              e.target.src = '/placeholder-car.jpg'; // Fallback image
            }}
          />
        ) : (
          <div className="image-placeholder">
            🚗
          </div>
        )}
      </div>
      
      <div className="cab-details">
        <h3 className="cab-type">{vehicle.type}</h3>
        
        <div className="cab-info">
          <div className="info-item">
            <span className="info-label">Seats:</span>
            <span className="info-value">{vehicle.seats}</span>
          </div>
          
          {vehicle.pricePerKm && (
            <div className="info-item">
              <span className="info-label">Price:</span>
              <span className="info-value">₹{vehicle.pricePerKm}/km</span>
            </div>
          )}
          
          {vehicle.description && (
            <div className="info-item">
              <span className="info-label">Description:</span>
              <span className="info-value">{vehicle.description}</span>
            </div>
          )}
        </div>
        
        <button 
          className="btn btn-primary book-btn"
          onClick={handleBookNow}
        >
          Book Now
        </button>
      </div>
    </div>
  );
};

export default CabCard;
