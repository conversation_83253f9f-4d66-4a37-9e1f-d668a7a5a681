import React, { useState } from 'react';
import {
  createBooking,
  getBookings,
  getBookingsByStatus,
  getTodaysBookings,
  updateBookingStatus,
  deleteBooking,
  getBookingStats,
  confirmBooking,
  completeBooking,
  cancelBooking
} from '../api/bookings';

const BookingAPITest = () => {
  const [results, setResults] = useState('');
  const [loading, setLoading] = useState(false);

  const log = (message) => {
    setResults(prev => prev + '\n' + message);
    console.log(message);
  };

  const testCreateBooking = async () => {
    setLoading(true);
    try {
      const testBooking = {
        name: 'Test User',
        phone: '+1234567890',
        email: '<EMAIL>',
        pickup: 'Test Pickup Location',
        drop: 'Test Drop Location',
        date: '2024-12-31',
        time: '14:30',
        vehicleType: 'Sedan',
        distance: 10,
        notes: 'Test booking from API test'
      };

      const result = await createBooking(testBooking);
      log(`✅ CREATE: ${JSON.stringify(result, null, 2)}`);
    } catch (error) {
      log(`❌ CREATE ERROR: ${error.message}`);
    }
    setLoading(false);
  };

  const testGetBookings = async () => {
    setLoading(true);
    try {
      const result = await getBookings();
      log(`✅ GET ALL: Found ${result.length} bookings`);
      log(JSON.stringify(result, null, 2));
    } catch (error) {
      log(`❌ GET ALL ERROR: ${error.message}`);
    }
    setLoading(false);
  };

  const testGetByStatus = async () => {
    setLoading(true);
    try {
      const pending = await getBookingsByStatus('pending');
      const confirmed = await getBookingsByStatus('confirmed');
      log(`✅ GET BY STATUS: ${pending.length} pending, ${confirmed.length} confirmed`);
    } catch (error) {
      log(`❌ GET BY STATUS ERROR: ${error.message}`);
    }
    setLoading(false);
  };

  const testGetTodaysBookings = async () => {
    setLoading(true);
    try {
      const result = await getTodaysBookings();
      log(`✅ TODAY'S BOOKINGS: Found ${result.length} bookings for today`);
    } catch (error) {
      log(`❌ TODAY'S BOOKINGS ERROR: ${error.message}`);
    }
    setLoading(false);
  };

  const testGetStats = async () => {
    setLoading(true);
    try {
      const stats = await getBookingStats();
      log(`✅ STATS: ${JSON.stringify(stats, null, 2)}`);
    } catch (error) {
      log(`❌ STATS ERROR: ${error.message}`);
    }
    setLoading(false);
  };

  const testStatusUpdates = async () => {
    setLoading(true);
    try {
      // This would need a real booking ID
      const bookingId = '1';
      
      log('🔄 Testing status updates (using mock ID)...');
      
      // Test individual status update methods
      await confirmBooking(bookingId);
      log('✅ Confirmed booking');
      
      await completeBooking(bookingId);
      log('✅ Completed booking');
      
      await cancelBooking(bookingId);
      log('✅ Cancelled booking');
      
    } catch (error) {
      log(`❌ STATUS UPDATE ERROR: ${error.message}`);
    }
    setLoading(false);
  };

  const runAllTests = async () => {
    setResults('🚀 Starting Booking API Tests...\n');
    
    await testCreateBooking();
    await testGetBookings();
    await testGetByStatus();
    await testGetTodaysBookings();
    await testGetStats();
    await testStatusUpdates();
    
    log('\n✨ All tests completed!');
  };

  const clearResults = () => {
    setResults('');
  };

  return (
    <div style={{ padding: '2rem', maxWidth: '800px', margin: '0 auto' }}>
      <h2>Booking API Test Console</h2>
      <p>Test all the booking API endpoints and functionality.</p>
      
      <div style={{ marginBottom: '1rem', display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
        <button onClick={testCreateBooking} disabled={loading} className="btn btn-primary btn-sm">
          Test Create
        </button>
        <button onClick={testGetBookings} disabled={loading} className="btn btn-primary btn-sm">
          Test Get All
        </button>
        <button onClick={testGetByStatus} disabled={loading} className="btn btn-primary btn-sm">
          Test Get by Status
        </button>
        <button onClick={testGetTodaysBookings} disabled={loading} className="btn btn-primary btn-sm">
          Test Today's
        </button>
        <button onClick={testGetStats} disabled={loading} className="btn btn-primary btn-sm">
          Test Stats
        </button>
        <button onClick={testStatusUpdates} disabled={loading} className="btn btn-primary btn-sm">
          Test Status Updates
        </button>
        <button onClick={runAllTests} disabled={loading} className="btn btn-success">
          Run All Tests
        </button>
        <button onClick={clearResults} className="btn btn-secondary btn-sm">
          Clear
        </button>
      </div>

      {loading && <div className="loading"><div className="spinner"></div><p>Running test...</p></div>}

      <div style={{
        background: '#1e1e1e',
        color: '#00ff00',
        padding: '1rem',
        borderRadius: '8px',
        fontFamily: 'monospace',
        fontSize: '0.9rem',
        minHeight: '300px',
        whiteSpace: 'pre-wrap',
        overflow: 'auto'
      }}>
        {results || 'Click a test button to see results...'}
      </div>

      <div style={{ marginTop: '2rem', padding: '1rem', background: '#f8f9fa', borderRadius: '8px' }}>
        <h3>API Endpoints Tested:</h3>
        <ul>
          <li><strong>POST /api/bookings</strong> - Create new booking</li>
          <li><strong>GET /api/bookings</strong> - Get all bookings with filters</li>
          <li><strong>GET /api/bookings?status=pending</strong> - Get bookings by status</li>
          <li><strong>GET /api/bookings?date=today</strong> - Get today's bookings</li>
          <li><strong>PUT /api/bookings/:id/status</strong> - Update booking status</li>
          <li><strong>DELETE /api/bookings/:id</strong> - Delete booking</li>
          <li><strong>Statistics calculation</strong> - Get booking analytics</li>
        </ul>
      </div>
    </div>
  );
};

export default BookingAPITest;
