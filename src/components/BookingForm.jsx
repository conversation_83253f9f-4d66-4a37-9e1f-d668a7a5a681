import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { createBooking } from '../api/bookings';
import { getVehicles } from '../api/vehicles';
import '../styles/BookingForm.css';

const BookingForm = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    pickup: '',
    drop: '',
    date: '',
    time: '',
    vehicleType: location.state?.vehicleType || '',
    vehicleId: location.state?.vehicleId || '',
    distance: '',
    notes: ''
  });

  useEffect(() => {
    fetchVehicles();
  }, []);

  const fetchVehicles = async () => {
    try {
      const data = await getVehicles();
      setVehicles(data);
    } catch (error) {
      console.error('Error fetching vehicles:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^\+?[\d\s\-\(\)]{10,}$/.test(formData.phone.trim())) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    // Email validation (optional field)
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email.trim())) {
      newErrors.email = 'Please enter a valid email address';
    }
    
    if (!formData.pickup.trim()) {
      newErrors.pickup = 'Pickup location is required';
    }

    if (!formData.drop.trim()) {
      newErrors.drop = 'Drop location is required';
    }
    
    if (!formData.date) {
      newErrors.date = 'Date is required';
    } else {
      const selectedDate = new Date(formData.date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (selectedDate < today) {
        newErrors.date = 'Date cannot be in the past';
      }
    }
    
    if (!formData.time) {
      newErrors.time = 'Time is required';
    } else if (formData.date) {
      // Validate time is not in the past for today's bookings
      const selectedDateTime = new Date(`${formData.date}T${formData.time}`);
      if (selectedDateTime < new Date()) {
        newErrors.time = 'Time cannot be in the past';
      }
    }

    if (!formData.vehicleType) {
      newErrors.vehicleType = 'Vehicle type is required';
    }

    // Distance validation (optional field)
    if (formData.distance && (isNaN(formData.distance) || parseFloat(formData.distance) <= 0)) {
      newErrors.distance = 'Distance must be a positive number';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    
    try {
      await createBooking(formData);
      navigate('/success', { 
        state: { 
          bookingDetails: formData 
        } 
      });
    } catch (error) {
      console.error('Error creating booking:', error);
      alert('Failed to create booking. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Get minimum date (today)
  const getMinDate = () => {
    const today = new Date();
    return today.toISOString().split('T')[0];
  };

  return (
    <div className="booking-form-container">
      <div className="container">
        <div className="booking-form-wrapper">
          <h1>Book Your Cab</h1>
          
          <form onSubmit={handleSubmit} className="booking-form">
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="name" className="form-label">Full Name *</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={`form-control ${errors.name ? 'error' : ''}`}
                  placeholder="Enter your full name"
                />
                {errors.name && <div className="error-message">{errors.name}</div>}
              </div>
              
              <div className="form-group">
                <label htmlFor="phone" className="form-label">Phone Number *</label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className={`form-control ${errors.phone ? 'error' : ''}`}
                  placeholder="Enter your phone number"
                />
                {errors.phone && <div className="error-message">{errors.phone}</div>}
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="email" className="form-label">Email Address</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className={`form-control ${errors.email ? 'error' : ''}`}
                placeholder="Enter your email address (optional)"
              />
              {errors.email && <div className="error-message">{errors.email}</div>}
            </div>
            
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="pickup" className="form-label">Pickup Location *</label>
                <input
                  type="text"
                  id="pickup"
                  name="pickup"
                  value={formData.pickup}
                  onChange={handleInputChange}
                  className={`form-control ${errors.pickup ? 'error' : ''}`}
                  placeholder="Enter pickup location"
                />
                {errors.pickup && <div className="error-message">{errors.pickup}</div>}
              </div>

              <div className="form-group">
                <label htmlFor="drop" className="form-label">Drop Location *</label>
                <input
                  type="text"
                  id="drop"
                  name="drop"
                  value={formData.drop}
                  onChange={handleInputChange}
                  className={`form-control ${errors.drop ? 'error' : ''}`}
                  placeholder="Enter drop location"
                />
                {errors.drop && <div className="error-message">{errors.drop}</div>}
              </div>
            </div>
            
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="date" className="form-label">Date *</label>
                <input
                  type="date"
                  id="date"
                  name="date"
                  value={formData.date}
                  onChange={handleInputChange}
                  min={getMinDate()}
                  className={`form-control ${errors.date ? 'error' : ''}`}
                />
                {errors.date && <div className="error-message">{errors.date}</div>}
              </div>
              
              <div className="form-group">
                <label htmlFor="time" className="form-label">Time *</label>
                <input
                  type="time"
                  id="time"
                  name="time"
                  value={formData.time}
                  onChange={handleInputChange}
                  className={`form-control ${errors.time ? 'error' : ''}`}
                />
                {errors.time && <div className="error-message">{errors.time}</div>}
              </div>
            </div>
            
            <div className="form-group">
              <label htmlFor="vehicleType" className="form-label">Vehicle Type *</label>
              <select
                id="vehicleType"
                name="vehicleType"
                value={formData.vehicleType}
                onChange={handleInputChange}
                className={`form-control ${errors.vehicleType ? 'error' : ''}`}
              >
                <option value="">Select a vehicle type</option>
                {vehicles.map(vehicle => (
                  <option key={vehicle.id} value={vehicle.type}>
                    {vehicle.type} - {vehicle.seats} seats
                  </option>
                ))}
              </select>
              {errors.vehicleType && <div className="error-message">{errors.vehicleType}</div>}
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="distance" className="form-label">Distance (km)</label>
                <input
                  type="number"
                  id="distance"
                  name="distance"
                  value={formData.distance}
                  onChange={handleInputChange}
                  className={`form-control ${errors.distance ? 'error' : ''}`}
                  placeholder="Estimated distance in kilometers"
                  min="0"
                  step="0.1"
                />
                {errors.distance && <div className="error-message">{errors.distance}</div>}
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="notes" className="form-label">Special Notes</label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                className="form-control"
                placeholder="Any special instructions or notes (optional)"
                rows="3"
              />
            </div>
            
            <div className="form-actions">
              <button
                type="button"
                onClick={() => navigate('/')}
                className="btn btn-secondary"
              >
                Cancel
              </button>
              
              <button
                type="submit"
                disabled={loading}
                className="btn btn-primary"
              >
                {loading ? 'Booking...' : 'Book Cab'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default BookingForm;
